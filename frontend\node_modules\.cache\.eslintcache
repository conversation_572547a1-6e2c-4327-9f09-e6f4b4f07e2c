[{"D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js": "1", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js": "2", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js": "3", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js": "4", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js": "5", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js": "6", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js": "7", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js": "8", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js": "9", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js": "10", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js": "11", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js": "12", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js": "13", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js": "14", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js": "15", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js": "16", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js": "17", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js": "18", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js": "19", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js": "20", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js": "21", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js": "22", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js": "23", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js": "24", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js": "25", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js": "26", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js": "27", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js": "28", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CivilianSystems.js": "29", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\JudicialSystems.js": "30", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProfessionSystems.js": "31", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SettingsManagement.js": "32", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeManagement.js": "33", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ContentManagement.js": "34", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\utils\\api.js": "35"}, {"size": 254, "mtime": 1748329246695, "results": "36", "hashOfConfig": "37"}, {"size": 5097, "mtime": 1748510171456, "results": "38", "hashOfConfig": "37"}, {"size": 7043, "mtime": 1748590293768, "results": "39", "hashOfConfig": "37"}, {"size": 10634, "mtime": 1748587566414, "results": "40", "hashOfConfig": "37"}, {"size": 19188, "mtime": 1748508012050, "results": "41", "hashOfConfig": "37"}, {"size": 10528, "mtime": 1748588373299, "results": "42", "hashOfConfig": "37"}, {"size": 13777, "mtime": 1748587985171, "results": "43", "hashOfConfig": "37"}, {"size": 20876, "mtime": 1748508046738, "results": "44", "hashOfConfig": "37"}, {"size": 29827, "mtime": 1748483630000, "results": "45", "hashOfConfig": "37"}, {"size": 24215, "mtime": 1748352877767, "results": "46", "hashOfConfig": "37"}, {"size": 23678, "mtime": 1748508088969, "results": "47", "hashOfConfig": "37"}, {"size": 23306, "mtime": 1748508119220, "results": "48", "hashOfConfig": "37"}, {"size": 44589, "mtime": 1748502631872, "results": "49", "hashOfConfig": "37"}, {"size": 19283, "mtime": 1748352994687, "results": "50", "hashOfConfig": "37"}, {"size": 4503, "mtime": 1748341004213, "results": "51", "hashOfConfig": "37"}, {"size": 26386, "mtime": 1748502592952, "results": "52", "hashOfConfig": "37"}, {"size": 5825, "mtime": 1748502680240, "results": "53", "hashOfConfig": "37"}, {"size": 8260, "mtime": 1748502719538, "results": "54", "hashOfConfig": "37"}, {"size": 17140, "mtime": 1748508543368, "results": "55", "hashOfConfig": "37"}, {"size": 18365, "mtime": 1748508505094, "results": "56", "hashOfConfig": "37"}, {"size": 15353, "mtime": 1748486959232, "results": "57", "hashOfConfig": "37"}, {"size": 15084, "mtime": 1748486881151, "results": "58", "hashOfConfig": "37"}, {"size": 15425, "mtime": 1748517744009, "results": "59", "hashOfConfig": "37"}, {"size": 18070, "mtime": 1748508192048, "results": "60", "hashOfConfig": "37"}, {"size": 21237, "mtime": 1748508478504, "results": "61", "hashOfConfig": "37"}, {"size": 19315, "mtime": 1748508250630, "results": "62", "hashOfConfig": "37"}, {"size": 9772, "mtime": 1748488625998, "results": "63", "hashOfConfig": "37"}, {"size": 32163, "mtime": 1748508148176, "results": "64", "hashOfConfig": "37"}, {"size": 10799, "mtime": 1748496033772, "results": "65", "hashOfConfig": "37"}, {"size": 10233, "mtime": 1748496079485, "results": "66", "hashOfConfig": "37"}, {"size": 9052, "mtime": 1748496118220, "results": "67", "hashOfConfig": "37"}, {"size": 9020, "mtime": 1748510223875, "results": "68", "hashOfConfig": "37"}, {"size": 7672, "mtime": 1748510265477, "results": "69", "hashOfConfig": "37"}, {"size": 5064, "mtime": 1748517526989, "results": "70", "hashOfConfig": "37"}, {"size": 3940, "mtime": 1748587546277, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js", ["177"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js", ["178", "179", "180", "181"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js", ["182"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js", ["183"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js", ["184", "185"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js", ["186"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js", ["187", "188"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js", ["189", "190", "191"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js", ["192", "193"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js", ["194"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js", ["195", "196"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js", ["197"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js", ["198", "199", "200"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js", ["201", "202"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js", ["203"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js", ["204", "205"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js", ["206", "207"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js", ["208"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js", ["209"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js", ["210", "211"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js", ["212"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js", ["213", "214", "215", "216", "217", "218", "219", "220", "221"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CivilianSystems.js", ["222", "223", "224", "225", "226"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\JudicialSystems.js", ["227", "228", "229"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProfessionSystems.js", ["230", "231", "232", "233", "234"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SettingsManagement.js", ["235"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeManagement.js", ["236", "237", "238"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ContentManagement.js", ["239"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\utils\\api.js", [], [], {"ruleId": "240", "severity": 1, "message": "241", "line": 111, "column": 6, "nodeType": "242", "endLine": 111, "endColumn": 8, "suggestions": "243"}, {"ruleId": "244", "severity": 1, "message": "245", "line": 23, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 23, "endColumn": 9}, {"ruleId": "244", "severity": 1, "message": "248", "line": 36, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 36, "endColumn": 17}, {"ruleId": "244", "severity": 1, "message": "249", "line": 39, "column": 22, "nodeType": "246", "messageId": "247", "endLine": 39, "endColumn": 31}, {"ruleId": "240", "severity": 1, "message": "250", "line": 110, "column": 6, "nodeType": "242", "endLine": 110, "endColumn": 8, "suggestions": "251"}, {"ruleId": "244", "severity": 1, "message": "252", "line": 106, "column": 13, "nodeType": "246", "messageId": "247", "endLine": 106, "endColumn": 21}, {"ruleId": "240", "severity": 1, "message": "253", "line": 115, "column": 6, "nodeType": "242", "endLine": 115, "endColumn": 8, "suggestions": "254"}, {"ruleId": "244", "severity": 1, "message": "255", "line": 19, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 19, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "256", "line": 116, "column": 6, "nodeType": "242", "endLine": 116, "endColumn": 8, "suggestions": "257"}, {"ruleId": "240", "severity": 1, "message": "258", "line": 131, "column": 6, "nodeType": "242", "endLine": 131, "endColumn": 8, "suggestions": "259"}, {"ruleId": "240", "severity": 1, "message": "260", "line": 112, "column": 6, "nodeType": "242", "endLine": 112, "endColumn": 8, "suggestions": "261"}, {"ruleId": "244", "severity": 1, "message": "262", "line": 356, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 356, "endColumn": 23}, {"ruleId": "244", "severity": 1, "message": "263", "line": 24, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 24, "endColumn": 10}, {"ruleId": "244", "severity": 1, "message": "264", "line": 34, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 34, "endColumn": 18}, {"ruleId": "240", "severity": 1, "message": "265", "line": 151, "column": 6, "nodeType": "242", "endLine": 151, "endColumn": 8, "suggestions": "266"}, {"ruleId": "244", "severity": 1, "message": "263", "line": 20, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 20, "endColumn": 10}, {"ruleId": "240", "severity": 1, "message": "267", "line": 115, "column": 6, "nodeType": "242", "endLine": 115, "endColumn": 8, "suggestions": "268"}, {"ruleId": "244", "severity": 1, "message": "269", "line": 23, "column": 16, "nodeType": "246", "messageId": "247", "endLine": 23, "endColumn": 20}, {"ruleId": "244", "severity": 1, "message": "270", "line": 108, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 108, "endColumn": 19}, {"ruleId": "240", "severity": 1, "message": "271", "line": 371, "column": 6, "nodeType": "242", "endLine": 371, "endColumn": 8, "suggestions": "272"}, {"ruleId": "240", "severity": 1, "message": "273", "line": 133, "column": 6, "nodeType": "242", "endLine": 133, "endColumn": 8, "suggestions": "274"}, {"ruleId": "244", "severity": 1, "message": "275", "line": 23, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 23, "endColumn": 11}, {"ruleId": "244", "severity": 1, "message": "276", "line": 34, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 34, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "277", "line": 103, "column": 6, "nodeType": "242", "endLine": 103, "endColumn": 17, "suggestions": "278"}, {"ruleId": "244", "severity": 1, "message": "279", "line": 32, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 32, "endColumn": 17}, {"ruleId": "240", "severity": 1, "message": "280", "line": 113, "column": 6, "nodeType": "242", "endLine": 113, "endColumn": 17, "suggestions": "281"}, {"ruleId": "240", "severity": 1, "message": "282", "line": 87, "column": 6, "nodeType": "242", "endLine": 87, "endColumn": 17, "suggestions": "283"}, {"ruleId": "244", "severity": 1, "message": "275", "line": 21, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 21, "endColumn": 11}, {"ruleId": "240", "severity": 1, "message": "284", "line": 81, "column": 6, "nodeType": "242", "endLine": 81, "endColumn": 17, "suggestions": "285"}, {"ruleId": "244", "severity": 1, "message": "275", "line": 23, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 23, "endColumn": 11}, {"ruleId": "240", "severity": 1, "message": "286", "line": 88, "column": 6, "nodeType": "242", "endLine": 88, "endColumn": 17, "suggestions": "287"}, {"ruleId": "240", "severity": 1, "message": "288", "line": 113, "column": 6, "nodeType": "242", "endLine": 113, "endColumn": 17, "suggestions": "289"}, {"ruleId": "240", "severity": 1, "message": "290", "line": 141, "column": 6, "nodeType": "242", "endLine": 141, "endColumn": 17, "suggestions": "291"}, {"ruleId": "244", "severity": 1, "message": "292", "line": 32, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 32, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "293", "line": 107, "column": 6, "nodeType": "242", "endLine": 107, "endColumn": 17, "suggestions": "294"}, {"ruleId": "244", "severity": 1, "message": "263", "line": 16, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 16, "endColumn": 10}, {"ruleId": "244", "severity": 1, "message": "295", "line": 26, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 26, "endColumn": 8}, {"ruleId": "244", "severity": 1, "message": "296", "line": 36, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 36, "endColumn": 22}, {"ruleId": "244", "severity": 1, "message": "297", "line": 39, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 39, "endColumn": 22}, {"ruleId": "244", "severity": 1, "message": "298", "line": 40, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 40, "endColumn": 19}, {"ruleId": "244", "severity": 1, "message": "299", "line": 46, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 46, "endColumn": 14}, {"ruleId": "244", "severity": 1, "message": "300", "line": 47, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 47, "endColumn": 16}, {"ruleId": "244", "severity": 1, "message": "301", "line": 61, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 61, "endColumn": 26}, {"ruleId": "240", "severity": 1, "message": "302", "line": 184, "column": 6, "nodeType": "242", "endLine": 184, "endColumn": 8, "suggestions": "303"}, {"ruleId": "244", "severity": 1, "message": "304", "line": 189, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 189, "endColumn": 26}, {"ruleId": "244", "severity": 1, "message": "298", "line": 29, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 29, "endColumn": 19}, {"ruleId": "244", "severity": 1, "message": "305", "line": 35, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 35, "endColumn": 15}, {"ruleId": "244", "severity": 1, "message": "306", "line": 47, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 47, "endColumn": 27}, {"ruleId": "244", "severity": 1, "message": "307", "line": 61, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 61, "endColumn": 25}, {"ruleId": "240", "severity": 1, "message": "308", "line": 74, "column": 6, "nodeType": "242", "endLine": 74, "endColumn": 17, "suggestions": "309"}, {"ruleId": "244", "severity": 1, "message": "310", "line": 19, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 19, "endColumn": 12}, {"ruleId": "244", "severity": 1, "message": "311", "line": 29, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 29, "endColumn": 17}, {"ruleId": "240", "severity": 1, "message": "308", "line": 64, "column": 6, "nodeType": "242", "endLine": 64, "endColumn": 17, "suggestions": "312"}, {"ruleId": "244", "severity": 1, "message": "310", "line": 19, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 19, "endColumn": 12}, {"ruleId": "244", "severity": 1, "message": "313", "line": 29, "column": 3, "nodeType": "246", "messageId": "247", "endLine": 29, "endColumn": 15}, {"ruleId": "244", "severity": 1, "message": "305", "line": 35, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 35, "endColumn": 15}, {"ruleId": "244", "severity": 1, "message": "314", "line": 47, "column": 9, "nodeType": "246", "messageId": "247", "endLine": 47, "endColumn": 34}, {"ruleId": "240", "severity": 1, "message": "308", "line": 64, "column": 6, "nodeType": "242", "endLine": 64, "endColumn": 17, "suggestions": "315"}, {"ruleId": "244", "severity": 1, "message": "316", "line": 1, "column": 17, "nodeType": "246", "messageId": "247", "endLine": 1, "endColumn": 25}, {"ruleId": "244", "severity": 1, "message": "317", "line": 30, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 30, "endColumn": 17}, {"ruleId": "244", "severity": 1, "message": "318", "line": 31, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 31, "endColumn": 17}, {"ruleId": "240", "severity": 1, "message": "319", "line": 71, "column": 6, "nodeType": "242", "endLine": 71, "endColumn": 10, "suggestions": "320"}, {"ruleId": "244", "severity": 1, "message": "316", "line": 1, "column": 17, "nodeType": "246", "messageId": "247", "endLine": 1, "endColumn": 25}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["321"], "no-unused-vars", "'Upload' is defined but never used.", "Identifier", "unusedVar", "'UploadOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'mockCharacters'. Either include it or remove the dependency array.", ["322"], "'response' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'mockFactions'. Either include it or remove the dependency array.", ["323"], "'Descriptions' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockSettings'. Either include it or remove the dependency array.", ["324"], "React Hook useEffect has a missing dependency: 'mockSystems'. Either include it or remove the dependency array.", ["325"], "React Hook useEffect has a missing dependency: 'mockPlots'. Either include it or remove the dependency array.", ["326"], "'completedPlots' is assigned a value but never used.", "'Divider' is defined but never used.", "'HistoryOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockEvents'. Either include it or remove the dependency array.", ["327"], "React Hook useEffect has a missing dependency: 'mockRelations'. Either include it or remove the dependency array.", ["328"], "'Text' is assigned a value but never used.", "'providers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAIInfo'. Either include it or remove the dependency array.", ["329"], "React Hook useEffect has a missing dependency: 'testAllAPIs'. Either include it or remove the dependency array.", ["330"], "'Progress' is defined but never used.", "'StarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEquipment'. Either include it or remove the dependency array.", ["331"], "'ShieldOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadPets'. Either include it or remove the dependency array.", ["332"], "React Hook useEffect has a missing dependency: 'loadRaces'. Either include it or remove the dependency array.", ["333"], "React Hook useEffect has a missing dependency: 'loadResources'. Either include it or remove the dependency array.", ["334"], "React Hook useEffect has a missing dependency: 'loadRealms'. Either include it or remove the dependency array.", ["335"], "React Hook useEffect has a missing dependency: 'loadDimensions'. Either include it or remove the dependency array.", ["336"], "React Hook useEffect has a missing dependency: 'loadTreasures'. Either include it or remove the dependency array.", ["337"], "'ShopOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLocations'. Either include it or remove the dependency array.", ["338"], "'Empty' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'OrderedListOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'Panel' is assigned a value but never used.", "'TabPane' is assigned a value but never used.", "'selectedVolumeId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'mockChapters' and 'mockVolumes'. Either include them or remove the dependency array.", ["339"], "'completedChapters' is assigned a value but never used.", "'Option' is assigned a value but never used.", "'socialClassOptions' is assigned a value but never used.", "'lifestyleOptions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSystems'. Either include it or remove the dependency array.", ["340"], "'Statistic' is defined but never used.", "'SafetyOutlined' is defined but never used.", ["341"], "'ToolOutlined' is defined but never used.", "'professionCategoryOptions' is assigned a value but never used.", ["342"], "'useState' is defined but never used.", "'loading' is assigned a value but never used.", "'volumes' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadVolumes'. Either include it or remove the dependency array.", ["343"], {"desc": "344", "fix": "345"}, {"desc": "346", "fix": "347"}, {"desc": "348", "fix": "349"}, {"desc": "350", "fix": "351"}, {"desc": "352", "fix": "353"}, {"desc": "354", "fix": "355"}, {"desc": "356", "fix": "357"}, {"desc": "358", "fix": "359"}, {"desc": "360", "fix": "361"}, {"desc": "362", "fix": "363"}, {"desc": "364", "fix": "365"}, {"desc": "366", "fix": "367"}, {"desc": "368", "fix": "369"}, {"desc": "370", "fix": "371"}, {"desc": "372", "fix": "373"}, {"desc": "374", "fix": "375"}, {"desc": "376", "fix": "377"}, {"desc": "378", "fix": "379"}, {"desc": "380", "fix": "381"}, {"desc": "382", "fix": "383"}, {"desc": "382", "fix": "384"}, {"desc": "382", "fix": "385"}, {"desc": "386", "fix": "387"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "388", "text": "389"}, "Update the dependencies array to be: [mockCharacters]", {"range": "390", "text": "391"}, "Update the dependencies array to be: [mockFactions]", {"range": "392", "text": "393"}, "Update the dependencies array to be: [mockSettings]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [mockSystems]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [mockPlots]", {"range": "398", "text": "399"}, "Update the dependencies array to be: [mockEvents]", {"range": "400", "text": "401"}, "Update the dependencies array to be: [mockRelations]", {"range": "402", "text": "403"}, "Update the dependencies array to be: [fetchAIInfo]", {"range": "404", "text": "405"}, "Update the dependencies array to be: [testAllAPIs]", {"range": "406", "text": "407"}, "Update the dependencies array to be: [loadEquipment, projectId]", {"range": "408", "text": "409"}, "Update the dependencies array to be: [loadPets, projectId]", {"range": "410", "text": "411"}, "Update the dependencies array to be: [loadRaces, projectId]", {"range": "412", "text": "413"}, "Update the dependencies array to be: [loadResources, projectId]", {"range": "414", "text": "415"}, "Update the dependencies array to be: [loadRealms, projectId]", {"range": "416", "text": "417"}, "Update the dependencies array to be: [loadDimensions, projectId]", {"range": "418", "text": "419"}, "Update the dependencies array to be: [loadTreasures, projectId]", {"range": "420", "text": "421"}, "Update the dependencies array to be: [loadLocations, projectId]", {"range": "422", "text": "423"}, "Update the dependencies array to be: [mockChapters, mockVolumes]", {"range": "424", "text": "425"}, "Update the dependencies array to be: [fetchSystems, projectId]", {"range": "426", "text": "427"}, {"range": "428", "text": "427"}, {"range": "429", "text": "427"}, "Update the dependencies array to be: [id, loadVolumes]", {"range": "430", "text": "431"}, [2963, 2965], "[fetchDashboardData]", [2480, 2482], "[mockCharacters]", [2547, 2549], "[mockFactions]", [2658, 2660], "[mockSettings]", [4240, 4242], "[mockSystems]", [2622, 2624], "[mockPlots]", [3567, 3569], "[mockEvents]", [2672, 2674], "[mockRelations]", [9568, 9570], "[fetchAIInfo]", [3445, 3447], "[testAllAPIs]", [2125, 2136], "[loadEquipment, projectId]", [2294, 2305], "[loadPets, projectId]", [1775, 1786], "[loadRaces, projectId]", [1716, 1727], "[loadResources, projectId]", [1877, 1888], "[loadRealms, projectId]", [2536, 2547], "[loadDimensions, projectId]", [3173, 3184], "[loadTreasures, projectId]", [2390, 2401], "[loadLocations, projectId]", [4452, 4454], "[mockChapters, mockVolumes]", [1755, 1766], "[fetchSystems, projectId]", [1396, 1407], [1500, 1511], [1306, 1310], "[id, loadVolumes]"]